import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { IS_PUBLIC_KEY } from "@repo/nestjs-commons/decorators/public.decorator";
import {
  ApiKeyAuthStrategy,
  BearerTokenHttpStrategy
} from "@repo/nestjs-commons/guards";
import * as rTracer from "cls-rtracer";
import { FastifyRequest } from "fastify";
import { PrivilegedAppAuthStrategy } from "../strategies/privileged-app-auth.strategy";

@Injectable()
export class PlatformAuthGuard implements CanActivate {
  constructor(
    private readonly apiKeyStrategy: ApiKeyAuthStrategy,
    private readonly bearerTokenStrategy: BearerTokenHttpStrategy,
    private readonly privilegedAppStrategy: PrivilegedAppAuthStrategy,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest<FastifyRequest>();

    try {
      const user = await this.authenticatedRequests(request);

      // Debug: Log the user object before conversion
      console.log('DEBUG: User before conversion:', JSON.stringify(user, null, 2));

      // Convert the commons User object to the platform-specific format
      const platformUser = {
        token: user.token || '',
        userType: user.userType,
        timezone: user.metadata?.timezone || 'UTC',
        sub: user.sub,
        email: user.email,
        orgId: user.orgId,
        uid: user.uid,
        orgUid: user.orgUid,
      };

      // Debug: Log the platform user object after conversion
      console.log('DEBUG: Platform user after conversion:', JSON.stringify(platformUser, null, 2));

      request.user = platformUser as any;

      // This is for logger context.
      const traceId = rTracer.id() as {
        reqId: string;
        context: Record<string, any>;
      };
      if (traceId && traceId.context) {
        traceId.context.userId = user?.uid;
        traceId.context.orgId = user?.orgUid;
      }

      return true;
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }

  /**
   * Authenticates the request using the appropriate strategy.
   * Prioritizes privileged app authentication when both X-api-key and X-user-ID are present.
   * @param request The request object.
   * @returns A promise that resolves to a User object.
   */
  private async authenticatedRequests(request: FastifyRequest) {
    // Check for privileged app authentication first
    if (PrivilegedAppAuthStrategy.shouldUse(request)) {
      return await this.privilegedAppStrategy.authenticate(request);
    }

    // Fall back to standard authentication strategies
    if (request.headers["x-api-key"]) {
      return await this.apiKeyStrategy.authenticate(request);
    }

    if (request.headers["authorization"]) {
      return await this.bearerTokenStrategy.authenticate(request);
    }

    throw new UnauthorizedException(
      "Authentication failed | No API key or Bearer token provided",
    );
  }
}
